{"packages": [{"name": "intervention/gif", "version": "4.2.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Intervention/gif.git", "reference": "5999eac6a39aa760fb803bc809e8909ee67b451a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Intervention/gif/zipball/5999eac6a39aa760fb803bc809e8909ee67b451a", "reference": "5999eac6a39aa760fb803bc809e8909ee67b451a", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"phpstan/phpstan": "^2.1", "phpunit/phpunit": "^10.0 || ^11.0  || ^12.0", "slevomat/coding-standard": "~8.0", "squizlabs/php_codesniffer": "^3.8"}, "time": "2025-03-29T07:46:21+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Intervention\\Gif\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://intervention.io/"}], "description": "Native PHP GIF Encoder/Decoder", "homepage": "https://github.com/intervention/gif", "keywords": ["animation", "gd", "gif", "image"], "support": {"issues": "https://github.com/Intervention/gif/issues", "source": "https://github.com/Intervention/gif/tree/4.2.2"}, "funding": [{"url": "https://paypal.me/interventionio", "type": "custom"}, {"url": "https://github.com/Intervention", "type": "github"}, {"url": "https://ko-fi.com/interventionphp", "type": "ko_fi"}], "install-path": "../intervention/gif"}, {"name": "intervention/image", "version": "3.11.3", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/Intervention/image.git", "reference": "d0f097b8a3fa8fb758efc9440b513aa3833cda17"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Intervention/image/zipball/d0f097b8a3fa8fb758efc9440b513aa3833cda17", "reference": "d0f097b8a3fa8fb758efc9440b513aa3833cda17", "shasum": ""}, "require": {"ext-mbstring": "*", "intervention/gif": "^4.2", "php": "^8.1"}, "require-dev": {"mockery/mockery": "^1.6", "phpstan/phpstan": "^2.1", "phpunit/phpunit": "^10.0 || ^11.0 || ^12.0", "slevomat/coding-standard": "~8.0", "squizlabs/php_codesniffer": "^3.8"}, "suggest": {"ext-exif": "Recommended to be able to read EXIF data properly."}, "time": "2025-05-22T17:26:23+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Intervention\\Image\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://intervention.io/"}], "description": "PHP image manipulation", "homepage": "https://image.intervention.io/", "keywords": ["gd", "image", "imagick", "resize", "thumbnail", "watermark"], "support": {"issues": "https://github.com/Intervention/image/issues", "source": "https://github.com/Intervention/image/tree/3.11.3"}, "funding": [{"url": "https://paypal.me/interventionio", "type": "custom"}, {"url": "https://github.com/Intervention", "type": "github"}, {"url": "https://ko-fi.com/interventionphp", "type": "ko_fi"}], "install-path": "../intervention/image"}, {"name": "intervention/image-driver-vips", "version": "1.0.6", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Intervention/image-driver-vips.git", "reference": "67f92c78cbe94a303f28d7ed84afd464eefac7aa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Intervention/image-driver-vips/zipball/67f92c78cbe94a303f28d7ed84afd464eefac7aa", "reference": "67f92c78cbe94a303f28d7ed84afd464eefac7aa", "shasum": ""}, "require": {"intervention/image": "^3.11.0", "jcupitt/vips": "^2.4", "php": "^8.1"}, "require-dev": {"ext-fileinfo": "*", "phpstan/phpstan": "^2", "phpunit/phpunit": "^10.0 || ^11.0 || ^12.0", "slevomat/coding-standard": "~8.0", "squizlabs/php_codesniffer": "^3.8"}, "time": "2025-06-10T14:30:21+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Intervention\\Image\\Drivers\\Vips\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://intervention.io/"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "libvips driver for Intervention Image", "homepage": "https://image.intervention.io/", "keywords": ["image", "libvips", "vips"], "support": {"issues": "https://github.com/Intervention/image-driver-vips/issues", "source": "https://github.com/Intervention/image-driver-vips/tree/1.0.6"}, "funding": [{"url": "https://paypal.me/interventionio", "type": "custom"}, {"url": "https://github.com/Intervention", "type": "github"}, {"url": "https://ko-fi.com/interventionphp", "type": "ko_fi"}], "install-path": "../intervention/image-driver-vips"}, {"name": "jcupitt/vips", "version": "v2.5.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/libvips/php-vips.git", "reference": "a54c1cceea581b592a199edd61a7c06f44a24c08"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/libvips/php-vips/zipball/a54c1cceea581b592a199edd61a7c06f44a24c08", "reference": "a54c1cceea581b592a199edd61a7c06f44a24c08", "shasum": ""}, "require": {"ext-ffi": "*", "php": ">=7.4", "psr/log": "^1.1.3|^2.0|^3.0"}, "require-dev": {"php-parallel-lint/php-parallel-lint": "^1.3", "phpdocumentor/shim": "^3.3", "phpunit/phpunit": "^9.5", "squizlabs/php_codesniffer": "^3.7"}, "time": "2025-04-04T17:10:13+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Jcupitt\\Vips\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/jcupitt", "role": "Developer"}], "description": "A high-level interface to the libvips image processing library.", "homepage": "https://github.com/libvips/php-vips", "keywords": ["image", "libvips", "processing"], "support": {"issues": "https://github.com/libvips/php-vips/issues", "source": "https://github.com/libvips/php-vips/tree/v2.5.0"}, "install-path": "../jcupitt/vips"}, {"name": "psr/log", "version": "3.0.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "f16e1d5863e37f8d8c2a01719f5b34baa2b714d3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/f16e1d5863e37f8d8c2a01719f5b34baa2b714d3", "reference": "f16e1d5863e37f8d8c2a01719f5b34baa2b714d3", "shasum": ""}, "require": {"php": ">=8.0.0"}, "time": "2024-09-11T13:17:53+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Log\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/3.0.2"}, "install-path": "../psr/log"}], "dev": true, "dev-package-names": []}