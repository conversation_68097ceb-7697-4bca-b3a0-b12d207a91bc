<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit13b99d8bc7d93dbb1e343c6ef5a482ba
{
    public static $files = array (
        '545b95e86adcdc11d1c161e3997378a0' => __DIR__ . '/../..' . '/app/Helpers/RajaGambarHelper.php',
        'e56202350ef55e3d2b05a29db850d68e' => __DIR__ . '/../..' . '/helpers/rajagambar_helpers.php',
    );

    public static $prefixLengthsPsr4 = array (
        'P' => 
        array (
            'Psr\\Log\\' => 8,
        ),
        'M' => 
        array (
            'Modules\\RajaGambar\\Tests\\' => 25,
            'Modules\\RajaGambar\\Database\\Seeders\\' => 36,
            'Modules\\RajaGambar\\Database\\Factories\\' => 38,
            'Modu<PERSON>\\RajaGambar\\' => 19,
        ),
        'J' => 
        array (
            'Jcupitt\\Vips\\' => 13,
        ),
        'I' => 
        array (
            'Intervention\\Image\\Drivers\\Vips\\' => 32,
            'Intervention\\Image\\' => 19,
            'Intervention\\Gif\\' => 17,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'Psr\\Log\\' => 
        array (
            0 => __DIR__ . '/..' . '/psr/log/src',
        ),
        'Modules\\RajaGambar\\Tests\\' => 
        array (
            0 => __DIR__ . '/../..' . '/tests',
        ),
        'Modules\\RajaGambar\\Database\\Seeders\\' => 
        array (
            0 => __DIR__ . '/../..' . '/database/seeders',
        ),
        'Modules\\RajaGambar\\Database\\Factories\\' => 
        array (
            0 => __DIR__ . '/../..' . '/database/factories',
        ),
        'Modules\\RajaGambar\\' => 
        array (
            0 => __DIR__ . '/../..' . '/app',
        ),
        'Jcupitt\\Vips\\' => 
        array (
            0 => __DIR__ . '/..' . '/jcupitt/vips/src',
        ),
        'Intervention\\Image\\Drivers\\Vips\\' => 
        array (
            0 => __DIR__ . '/..' . '/intervention/image-driver-vips/src',
        ),
        'Intervention\\Image\\' => 
        array (
            0 => __DIR__ . '/..' . '/intervention/image/src',
        ),
        'Intervention\\Gif\\' => 
        array (
            0 => __DIR__ . '/..' . '/intervention/gif/src',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit13b99d8bc7d93dbb1e343c6ef5a482ba::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit13b99d8bc7d93dbb1e343c6ef5a482ba::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInit13b99d8bc7d93dbb1e343c6ef5a482ba::$classMap;

        }, null, ClassLoader::class);
    }
}
