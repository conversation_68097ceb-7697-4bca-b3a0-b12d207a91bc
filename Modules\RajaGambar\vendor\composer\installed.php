<?php return array(
    'root' => array(
        'name' => 'nwidart/rajagambar',
        'pretty_version' => 'dev-main',
        'version' => 'dev-main',
        'reference' => '0296847852f655a2feec6e9b02ba0d9cc46500ce',
        'type' => 'library',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        'intervention/gif' => array(
            'pretty_version' => '4.2.2',
            'version' => '4.2.2.0',
            'reference' => '5999eac6a39aa760fb803bc809e8909ee67b451a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../intervention/gif',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'intervention/image' => array(
            'pretty_version' => '3.11.3',
            'version' => '********',
            'reference' => 'd0f097b8a3fa8fb758efc9440b513aa3833cda17',
            'type' => 'library',
            'install_path' => __DIR__ . '/../intervention/image',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'intervention/image-driver-vips' => array(
            'pretty_version' => '1.0.6',
            'version' => '*******',
            'reference' => '67f92c78cbe94a303f28d7ed84afd464eefac7aa',
            'type' => 'library',
            'install_path' => __DIR__ . '/../intervention/image-driver-vips',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'jcupitt/vips' => array(
            'pretty_version' => 'v2.5.0',
            'version' => '*******',
            'reference' => 'a54c1cceea581b592a199edd61a7c06f44a24c08',
            'type' => 'library',
            'install_path' => __DIR__ . '/../jcupitt/vips',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nwidart/rajagambar' => array(
            'pretty_version' => 'dev-main',
            'version' => 'dev-main',
            'reference' => '0296847852f655a2feec6e9b02ba0d9cc46500ce',
            'type' => 'library',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/log' => array(
            'pretty_version' => '3.0.2',
            'version' => '*******',
            'reference' => 'f16e1d5863e37f8d8c2a01719f5b34baa2b714d3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/log',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
